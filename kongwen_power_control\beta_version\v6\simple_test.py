#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的lj_env_1模型集成测试
"""

import sys
import os
from pathlib import Path

# 添加路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir / "production_deployment" / "src"))

def simple_test():
    """简化测试"""
    print("🧪 lj_env_1模型简化测试")
    print("="*40)
    
    try:
        # 1. 测试导入
        print("1. 测试导入...")
        from predict import VicePowerPredictor
        print("✅ 导入成功")
        
        # 2. 测试初始化
        print("2. 测试初始化...")
        predictor = VicePowerPredictor()
        print("✅ 初始化成功")
        
        # 3. 检查lj_env_1参数
        print("3. 检查lj_env_1参数...")
        if hasattr(predictor, 'lj_env_1_model_params'):
            params = predictor.lj_env_1_model_params
            print(f"✅ 参数存在: 截距={params['intercept']}, 重量系数={params['weight_coef']}, 硅热能系数={params['silicon_coef']}")
        else:
            print("❌ lj_env_1参数不存在")
            return False
        
        # 4. 测试预测
        print("4. 测试预测...")
        result = predictor.predict_single(200, 150, '首投')
        
        if result.get('predicted_vice_power_kwh') is not None:
            predicted_power = result['predicted_vice_power_kwh']
            model_used = result.get('model_used', 'Unknown')
            print(f"✅ 预测成功: {predicted_power:.2f}kWh (模型: {model_used})")
        else:
            print(f"❌ 预测失败: {result}")
            return False
        
        # 5. 测试多个案例
        print("5. 测试多个案例...")
        test_cases = [
            (50, 40, '首投'),
            (100, 80, '复投'),
            (300, 250, '首投'),
        ]
        
        for weight_diff, silicon_energy, process_type in test_cases:
            result = predictor.predict_single(weight_diff, silicon_energy, process_type)
            predicted_power = result.get('predicted_vice_power_kwh')
            
            if predicted_power is not None:
                print(f"  {weight_diff}kg + {silicon_energy}kWh ({process_type}) → {predicted_power:.2f}kWh")
            else:
                print(f"  {weight_diff}kg + {silicon_energy}kWh ({process_type}) → 失败")
        
        print("✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = simple_test()
    if success:
        print("\n🎉 lj_env_1模型集成成功！")
    else:
        print("\n❌ 集成失败！")
